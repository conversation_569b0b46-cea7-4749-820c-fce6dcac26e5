/* eslint-disable react-hooks/rules-of-hooks */
import ActionButton from '@/components/ActionButton';
import WhatsappButton from '@/components/WhatsappButton';
import 'animate.css';
import HomeText from '@/components/HomeText';
import { Statistic } from '@/components/Stats';
import SectionSale from '@/components/sectionSale';
import Partners from '@/components/partners';
import { TracingBeam } from '@/components/ui/tracing-beam';
export const dynamic = 'force-static';
export default function Home() {
  return (
    <section className="container relative max-w-[100vw] bg-white text-gray-900">
      <div className="prose relative mx-auto flex-col lg:prose-2xl md:px-14 md:py-10 lg:flex lg:max-w-screen-xl">
        <div className="mx-auto max-w-sm text-center md:max-w-3xl">
          <div className="relative isolate">
            <div
              aria-hidden="true"
              className="pointer-events-none absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
            >
              <div className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 clip-path-fbg sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]" />
            </div>
          </div>
        </div>
        <HomeText />
        <div className="mt-8 flex flex-col gap-3">
          <div className="mx-auto flex flex-row flex-wrap justify-center gap-4">
            <ActionButton />
          </div>
        </div>
        <Statistic />
        <Partners />
        <WhatsappButton />
      </div>
      <div className="prose relative flex-col py-10 lg:prose-2xl md:mx-auto lg:flex lg:max-w-screen-xl">
        <TracingBeam className="">
          <SectionSale />
        </TracingBeam>
      </div>
    </section>
  );
}
