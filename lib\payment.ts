import { ZodSchema, z } from 'zod';
import API from './axios';
import { CombinedType } from '@/types';
import { toast } from 'sonner';
import { NetworkError } from '@/types/error';

type PaymentMethod = {
  name: string;
  url: string;
  schemaData: ZodSchema;
};
class Agrregator {
  name: string;
  paymentMethods: PaymentMethod[];

  constructor(name: string, pM: PaymentMethod[]) {
    this.name = name;
    this.paymentMethods = pM;
  }

  async initializePayment(
    paymentMethod: PaymentMethod,
    dto: CombinedType,
    path: string,
  ): Promise<{ data: any; success: boolean }> {
    // Vous devrez implémenter cette méthode en fonction des appels réels à l'API d'initialisation du paiement
    // Elle devrait renvoyer une promesse avec le résultat du paiement.
    dto.lang = 'fr';
    const payload = paymentMethod.schemaData.parse(dto);
    // console.log(payload);

    try {
      const res = await API.post(paymentMethod.url, payload, {
        headers: {
          'Current-Path': path,
        },
      });
      return { data: res, success: true };
    } catch (error) {
      // console.log(error);

      throw error;
    }
  }
}

const mycoolPayMobilSchema = z.object({
  amount: z.number(),
  name: z.string(),
  email: z.string(),
  currency: z.string(),
  reference: z.string(),
  lang: z.string(),
  reason: z.string(),
  phone: z.number(),
  offre: z.string(),
  examen: z.string(),
  channel: z.string(),
  parrain: z.string().optional(),
});
const mycoolPayMobilMethod = {
  name: 'mobile',
  url: '/api/coolPayments/initialize',
  schemaData: mycoolPayMobilSchema,
};

// Liste des agrégateurs
export const AGGREGATORS = [
  new Agrregator('MycoolPay', [mycoolPayMobilMethod]),
];
// Fonction pour initialiser un paiement
export async function initGeneralPayment(
  paymentMethodName: string,
  dto: CombinedType,
  aggregators: Agrregator[],
  path: string,
) {
  let currentAggregatorIndex = 0;
  let retryCount = 0;

  while (currentAggregatorIndex < aggregators.length) {
    const currentAggregator = aggregators[currentAggregatorIndex];

    const paymentMethod = currentAggregator.paymentMethods.find(
      (pm) => (pm.name = paymentMethodName),
    );

    if (paymentMethod) {
      try {
        const paymentResult = await currentAggregator.initializePayment(
          paymentMethod,
          dto,
          path,
        );

        if (paymentResult.success) {
          return paymentResult.data;
        } else {
          retryCount++;

          if (retryCount >= 2) {
            currentAggregatorIndex++;
            retryCount = 0;
          }
        }
      } catch (error) {
        console.log(error);
        
        if (error instanceof NetworkError) {
          toast.error(error.message);
        }
        console.error(
          `Erreur lors de l'initialisation du paiement avec ${currentAggregator.name}: ${error}`,
        );
        retryCount++;

        if (retryCount >= 2) {
          currentAggregatorIndex++;
          retryCount = 0;
        }
      }
    } else {
      currentAggregatorIndex++;
    }
  }
  throw new Error('Aucun agrégateur disponible pour le paiement.');
}
