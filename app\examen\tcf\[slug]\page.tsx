import { getAuthSession } from '@/lib/auth';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';
import React from 'react';
import { fechSerie } from '../../actions';

import { buttonVariants } from '@/components/ui/button';
import { DisciplineCard } from '../_components/cards';
import { getFreeSerrie } from '@/lib/free-serie-helper';
interface Props {
  params: {
    slug: string;
  };
}
async function Page({ params: { slug } }: Props) {
  const session = await getAuthSession();

  const FREE_TCF = await getFreeSerrie('tcf');
  if (!session && !FREE_TCF.includes(slug)) redirect('/auth');

  const exist = await fechSerie(slug, session?.user?.accessToken || '');

  if (!exist) return notFound();

  return (
    <section className="relative mt-1 flex h-full flex-col items-center justify-center gap-5 md:container md:p-0">
      <Link
        href={'/examen/tcf'}
        className={buttonVariants({
          variant: 'outline',
          size: 'sm',
          className: 'absolute bottom-2 right-2 w-fit sm:left-3 sm:top-3',
        })}
      >
        <ArrowLeft className="h-5 w-5 text-blue-500" />
      </Link>
      <h1 className="flex flex-wrap items-center text-center text-lg">
        Vous êtes sur le point de commencer la série{' '}
        <strong className="mx-2">{slug}</strong> du TCF nouveau format
      </h1>
      <p className="prose text-center">
        Vous devez choisir une discipline pour débuter le test, bon
        apprentissage !
      </p>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <DisciplineCard
          serie={exist}
          type="CE"
          title="Comprehension ecrite"
          duration="60"
          questions={39}
          href="comprehension-ecrite"
          icon={<Icons.CE />}
        />
        <DisciplineCard
          serie={exist}
          type="CO"
          title="Comprehension orale"
          duration="40"
          questions={39}
          href="comprehension-orale"
          icon={<Icons.CO />}
        />
        <DisciplineCard
          serie={exist}
          type="EE"
          title="Expression ecrite"
          duration="60"
          questions={3}
          href="expression-ecrite"
          icon={<Icons.EE />}
        />
        <DisciplineCard
          serie={exist}
          type="EO"
          title="Expression orale"
          duration="12"
          questions={3}
          href="expression-orale"
          icon={<Icons.EO />}
        />
      </div>
    </section>
  );
}

export default Page;

const Icons = {
  CE: () => (
    <span className="h-20 w-20">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6 text-inherit"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"
        />
      </svg>
    </span>
  ),
  CO: () => (
    <span className="h-20 w-20">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6 text-inherit"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z"
        />
      </svg>
    </span>
  ),
  EE: () => (
    <span className="h-20 w-20">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6 text-inherit"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
        />
      </svg>
    </span>
  ),
  EO: () => (
    <span className="h-20 w-20">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6 text-inherit"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z"
        />
      </svg>
    </span>
  ),
};
