'use client';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import card from '@/public/master-card.svg';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';
import mtn from '@/public/momo.jpg';
import orange from '@/public/orange.jpg';
import { cn } from '@/lib/utils';
import PhoneForm from '../../checkout/PhoneForm';
import { v4 as uuid } from 'uuid';
import { EXAMS, METHODS } from '@/config';
import { useSession } from 'next-auth/react';
import { parseAsBoolean, parseAsStringLiteral, useQueryState } from 'nuqs';
import { Button } from '@/components/ui/button';
import API from '@/lib/axios';
import { toast as sonner } from 'sonner';
import { AxiosError } from 'axios';
import Link from 'next/link';
import { Checkbox } from '@/components/ui/checkbox';
import { getDayCount } from '@/lib/getDayCount';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronRight, ChevronLeft, Check } from 'lucide-react';
import { useDealerRecharge } from '@/hooks/recharge';
import { NumericFormat } from 'react-number-format';
import { useCountry } from '@/hooks/use-country';
function FormContent() {
  const { data: session } = useSession();

  // State for multi-step form
  const [step, setStep] = useState<number>(1);
  const totalSteps = 4; // 4 steps for all users now

  const [recall, setRecall] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);
  const [acceptTerms, setAcceptTerms] = useQueryState(
    'acceptTerms',
    parseAsBoolean.withDefault(false),
  );
  const [selectedOffer, setSelectedOffer] = useQueryState('selectedOffer');
  const [examen, setExamen] = useQueryState(
    'examen',
    parseAsStringLiteral(EXAMS),
  );
  const [method, setMethod] = useQueryState(
    'method',
    parseAsStringLiteral(METHODS).withDefault('ORANGE'),
  );

  // Fetch dealer recharge offers
  const { data: offers, isLoading: offersLoading } = useDealerRecharge();

  // Navigation functions
  const nextStep = () => setStep((prev) => Math.min(prev + 1, totalSteps));
  const prevStep = () => setStep((prev) => Math.max(prev - 1, 1));

  // Check if can proceed to next step
  const canProceedToStep2 = !!selectedOffer;
  const canProceedToStep3 = !!examen;
  const canProceedToStep4 = !!method;

  // Animation variants
  const variants = {
    enter: { opacity: 0, x: 100 },
    center: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -100 },
  };

  const ref = uuid();
  const { dealerContry, checkCountry } = useCountry();
  const createPayload = useCallback(
    (currency: string = 'XAF', phone?: string) => {
      const selectedOfferData = offers?.find(
        (offer) => offer._id === selectedOffer,
      );
      const amount =
        checkCountry === 'CMR'
          ? selectedOfferData?.cameroun.local
          : checkCountry === 'AFO'
            ? selectedOfferData?.afrique_ouest.local
            : selectedOfferData?.international.local;

      const payload: any = {
        amount: amount,
        currency: currency,
        reason: `recharge des soldes d'expression revendeur ${examen} du ${new Date(Date.now()).toISOString()} de (${amount} ${currency}) par ${method}`,
        reference: ref,
        email: session?.user.email!,
        name: session?.user.email!,
        channel: `cm.${method?.toLowerCase()}`,
        examen: examen,
        offre: selectedOfferData?._id,
      };
      if (phone) {
        payload.phone = phone;
      }
      return payload;
    },
    [
      offers,
      selectedOffer,
      examen,
      method,
      ref,
      session?.user.email,
      checkCountry,
    ],
  );

  const initiateCardPayment = async (payload: any) => {
    const selectedOfferData = offers?.find(
      (offer) => offer._id === selectedOffer,
    );
    try {
      setIsLoading(true);

      console.log('Payload envoyé :', payload);

      // Appel de l'API
      const response = await API.post('/api/coolPayments/init-cardpayment', {
        ...payload,
        amount:
          checkCountry === 'CMR'
            ? selectedOfferData?.cameroun.euro
            : checkCountry === 'AFO'
              ? selectedOfferData?.afrique_ouest.euro
              : selectedOfferData?.international.euro,
      });

      if (response.data?.status === 'success') {
        // Redirection vers l'URL de paiement
        const paymentUrl = response.data.payment_url;
        window.location.href = paymentUrl;
        setIsLoading(false); // Ouvre le paiement dans un nouvel onglet
      } else {
        console.error('Erreur dans la réponse :', response.data);
        sonner.error(
          "Une erreur est survenue lors de l'initialisation du paiement.",
        );
      }
    } catch (error) {
      console.error("Erreur lors de l'appel à l'API :", error);
      if (error instanceof AxiosError) {
        if (
          error.code === 'ERR_BAD_REQUEST' &&
          error.response?.data?.message ===
            '[customer_phone_number] : Invalid phone number !'
        ) {
          setRecall(true);
          return;
        }
      }
      sonner.error('Une erreur est survenue. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (recall) {
      sonner.promise(
        initiateCardPayment(createPayload('EUR', '+237686876873')),
        {
          loading: 'Initialisation du paiement...',
        },
      );
    }
  }, [recall, createPayload]);

  // Force TCF selection for dealers
  useEffect(() => {
    if (session?.user?.role === 'dealer' && !examen) {
      setExamen('TCF');
    }
  }, [session?.user?.role, examen, setExamen]);

  const remainTCF =
    session?.user?.role == 'admin'
      ? 100
      : getDayCount(session?.user?.remains?.remainTCF?.remain_day || null);

  const remainTEF =
    session?.user?.role == 'admin'
      ? 100
      : getDayCount(session?.user?.remains?.remainTEF?.remain_day || null);

  return (
    <div className="mx-auto max-w-2xl rounded-lg bg-white p-6 shadow-md">
      <h1 className="text-center text-2xl font-bold text-gray-800">
        Recharge dealer
      </h1>
      <p className="mt-4 max-w-[60ch] text-center text-lg font-bold text-gray-600">
        Augmentez votre quota de correction des sujets d&apos;expression ou de
        production écrite et orale, que vous soumettrez.
      </p>

      {/* Progress indicator */}
      <div className="my-8">
        <div className="flex justify-between">
          {Array.from({ length: totalSteps }, (_, i) => i + 1).map(
            (stepNumber) => (
              <div key={stepNumber} className="flex flex-col items-center">
                <div
                  className={cn(
                    'flex h-10 w-10 items-center justify-center rounded-full border-2',
                    step >= stepNumber
                      ? 'border-primary bg-primary text-white'
                      : 'border-gray-300 bg-white text-gray-400',
                  )}
                >
                  {step > stepNumber ? (
                    <Check className="h-5 w-5" />
                  ) : (
                    stepNumber
                  )}
                </div>
                <span className="mt-1 text-xs">
                  {stepNumber === 1
                    ? 'Offre'
                    : stepNumber === 2
                      ? 'Examen'
                      : stepNumber === 3
                        ? 'Méthode'
                        : 'Paiement'}
                </span>
              </div>
            ),
          )}
        </div>
        <div className="mt-2 h-2 rounded-full bg-gray-200">
          <div
            className="h-full rounded-full bg-primary transition-all duration-300"
            style={{ width: `${((step - 1) / (totalSteps - 1)) * 100}%` }}
          />
        </div>
      </div>

      {/* Form steps */}
      <AnimatePresence mode="wait">
        <motion.div
          key={step}
          initial="enter"
          animate="center"
          exit="exit"
          variants={variants}
          transition={{ duration: 0.3 }}
          className="mb-20 sm:mb-0"
        >
          {step === 1 && (
            <div className="space-y-6">
              <div className="rounded-lg border bg-gray-50 p-6">
                <h3 className="mb-4 text-lg font-medium">
                  Sélectionnez votre offre de recharge
                </h3>

                {offersLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2">
                    {offers?.map((offer) => (
                      <div
                        key={offer._id}
                        className={cn(
                          'cursor-pointer rounded-lg border p-4 transition-colors',
                          selectedOffer === offer._id
                            ? 'border-primary bg-primary/5'
                            : 'hover:border-primary',
                        )}
                        onClick={() => setSelectedOffer(offer._id)}
                      >
                        <div className="space-y-3">
                          <h4 className="text-lg font-semibold">
                            {offer.title}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {offer.subtitle}
                          </p>

                          <div className="flex items-center justify-between">
                            {dealerContry != 'international' ? (
                              <div className="text-2xl font-bold text-primary">
                                <NumericFormat
                                  displayType="text"
                                  value={offer[dealerContry].local}
                                  thousandSeparator=" "
                                />{' '}
                                F
                                <span className="ml-2 text-sm font-normal text-gray-500">
                                  /{' '}
                                  <NumericFormat
                                    displayType="text"
                                    value={offer[dealerContry].euro}
                                    thousandSeparator=" "
                                  />{' '}
                                  €
                                </span>
                              </div>
                            ) : (
                              <div className="text-2xl font-bold text-primary">
                                <NumericFormat
                                  displayType="text"
                                  value={offer.international.local}
                                  thousandSeparator=" "
                                />{' '}
                                €
                              </div>
                            )}
                          </div>

                          <div className="space-y-1 text-sm">
                            <p>
                              <span className="font-medium">
                                Expression écrite:
                              </span>{' '}
                              +{offer.soldeEE} crédits
                            </p>
                            <p>
                              <span className="font-medium">
                                Expression orale:
                              </span>{' '}
                              +{offer.soldeEO} crédits
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="hidden justify-end md:flex">
                <Button
                  onClick={nextStep}
                  disabled={!canProceedToStep2}
                  className="flex items-center"
                >
                  Suivant <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6">
              <div className="rounded-lg border bg-gray-50 p-6">
                <h3 className="mb-4 text-lg font-medium">
                  Sélectionnez votre examen
                </h3>
                {session?.user?.role === 'dealer' && (
                  <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-3">
                    <p className="text-sm text-blue-700">
                      En tant que dealer, vous êtes automatiquement configuré
                      pour TCF.
                    </p>
                  </div>
                )}
                <RadioGroup
                  disabled={isLoading}
                  onValueChange={(val) => setExamen(val as any)}
                  defaultValue={
                    session?.user?.role === 'dealer'
                      ? 'TCF'
                      : examen || undefined
                  }
                  value={
                    session?.user?.role === 'dealer'
                      ? 'TCF'
                      : examen || undefined
                  }
                  className="flex items-center justify-center gap-5"
                >
                  <div className="flex items-center gap-3">
                    <RadioGroupItem
                      id="TCF"
                      value="TCF"
                      className="h-5 w-5"
                      disabled={remainTCF < 1}
                    />
                    <Label
                      className={cn('', {
                        'cursor-pointer': remainTCF > 0,
                        'cursor-not-allowed': remainTCF < 1,
                      })}
                      htmlFor="TCF"
                    >
                      TCF
                    </Label>
                  </div>
                  <div className="flex items-center gap-3">
                    <RadioGroupItem
                      id="TEF"
                      value="TEF"
                      className="h-5 w-5"
                      disabled={
                        session?.user?.role === 'dealer' || remainTEF < 1
                      }
                    />
                    <Label
                      className={cn('', {
                        'cursor-pointer':
                          session?.user?.role !== 'dealer' && remainTEF > 0,
                        'cursor-not-allowed':
                          session?.user?.role === 'dealer' || remainTEF < 1,
                      })}
                      htmlFor="TEF"
                    >
                      TEF
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="hidden justify-between md:flex">
                <Button
                  variant="outline"
                  onClick={prevStep}
                  className="flex items-center"
                >
                  <ChevronLeft className="mr-2 h-4 w-4" /> Précédent
                </Button>
                <Button
                  onClick={nextStep}
                  disabled={!canProceedToStep3}
                  className="flex items-center"
                >
                  Suivant <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-6">
              <div className="rounded-lg border bg-gray-50 p-6">
                <h3 className="mb-4 text-lg font-medium">
                  Choisissez votre méthode de paiement
                </h3>
                <RadioGroup
                  disabled={isLoading}
                  value={method || undefined}
                  onValueChange={(val) => setMethod(val as any)}
                  className="grid grid-cols-1 gap-4 md:grid-cols-3"
                >
                  <div
                    className={cn(
                      'rounded-lg border p-4 transition-colors',
                      method === 'MTN'
                        ? 'border-primary bg-primary/5'
                        : 'hover:border-primary',
                    )}
                  >
                    <RadioGroupItem id="MTN" value="MTN" className="sr-only" />
                    <Label
                      htmlFor="MTN"
                      className="flex cursor-pointer flex-col items-center justify-center gap-4"
                    >
                      <div className="h-16 w-16 overflow-hidden rounded-full">
                        <Image
                          src={mtn}
                          alt="MTN MoMo"
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <span>MTN MoMo</span>
                    </Label>
                  </div>

                  <div
                    className={cn(
                      'rounded-lg border p-4 transition-colors',
                      method === 'ORANGE'
                        ? 'border-primary bg-primary/5'
                        : 'hover:border-primary',
                    )}
                  >
                    <RadioGroupItem
                      id="ORANGE"
                      value="ORANGE"
                      className="sr-only"
                    />
                    <Label
                      htmlFor="ORANGE"
                      className="flex cursor-pointer flex-col items-center justify-center gap-4"
                    >
                      <div className="h-16 w-16 overflow-hidden rounded-full">
                        <Image
                          src={orange}
                          alt="Orange Money"
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <span>Orange Money</span>
                    </Label>
                  </div>

                  <div
                    className={cn(
                      'rounded-lg border p-4 transition-colors',
                      method === 'CARD'
                        ? 'border-primary bg-primary/5'
                        : 'hover:border-primary',
                    )}
                  >
                    <RadioGroupItem
                      id="CARD"
                      value="CARD"
                      className="sr-only"
                    />
                    <Label
                      htmlFor="CARD"
                      className="flex cursor-pointer flex-col items-center justify-center gap-4"
                    >
                      <div className="h-16 w-16 overflow-hidden rounded-full">
                        <Image
                          src={card}
                          alt="CARD"
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <span>VISA/MasterCard</span>
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="hidden justify-between md:flex">
                <Button
                  variant="outline"
                  onClick={prevStep}
                  className="flex items-center"
                >
                  <ChevronLeft className="mr-2 h-4 w-4" /> Précédent
                </Button>
                <Button
                  onClick={nextStep}
                  disabled={!canProceedToStep4}
                  className="flex items-center"
                >
                  Suivant <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {step === 4 && (
            <div className="space-y-6">
              <div className="rounded-lg border bg-gray-50 p-6">
                <h3 className="mb-4 text-lg font-medium">
                  Finaliser votre paiement
                </h3>

                {method === 'ORANGE' || method === 'MTN' ? (
                  <div className="mx-auto max-w-sm">
                    <PhoneForm
                      payload={createPayload()}
                      method={method as any}
                      buttonText="Payez maintenant"
                    />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="items-top flex space-x-2">
                      <Checkbox
                        id="terms1"
                        checked={acceptTerms}
                        onCheckedChange={(checked: any) =>
                          setAcceptTerms(checked)
                        }
                      />
                      <div className="grid gap-1.5 text-left leading-none">
                        <label
                          htmlFor="terms1"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {"J'accepte les conditions."}
                        </label>
                        <p className="text-sm text-muted-foreground">
                          En cochant cette case vous acceptez nos{' '}
                          <Link
                            href={'/condition-remboursement'}
                            className="underline underline-offset-4 hover:text-primary"
                          >
                            conditions de remboursement.
                          </Link>
                        </p>
                      </div>
                    </div>

                    <Button
                      disabled={!acceptTerms || isLoading}
                      className="w-full"
                      onClick={() =>
                        initiateCardPayment(
                          createPayload('EUR', session?.user.phone),
                        )
                      }
                    >
                      {isLoading ? 'Traitement en cours...' : 'Payer par carte'}
                    </Button>
                  </div>
                )}

                <div className="mt-6 text-sm text-gray-500">
                  <p>Récapitulatif:</p>
                  <ul className="mt-2 list-disc pl-5">
                    <li>
                      Offre:{' '}
                      {offers?.find((o) => o._id === selectedOffer)?.title}
                    </li>
                    <li>Examen: {examen || 'TCF'}</li>
                    <li>Méthode de paiement: {method}</li>
                    <li>
                      Montant:{' '}
                      {method === 'CARD'
                        ? '10 €'
                        : `${offers?.find((o) => o._id === selectedOffer)?.cameroun?.local || 5000} F`}
                    </li>
                  </ul>
                </div>
              </div>

              <div className="hidden justify-between md:flex">
                <Button
                  variant="outline"
                  onClick={prevStep}
                  className="flex items-center"
                >
                  <ChevronLeft className="mr-2 h-4 w-4" /> Précédent
                </Button>
              </div>
            </div>
          )}
        </motion.div>
      </AnimatePresence>

      {/* Mobile navigation bar */}
      <div className="fixed bottom-0 left-0 right-0 flex justify-between border-t bg-white p-4 sm:hidden">
        {step > 1 && (
          <Button
            variant="outline"
            onClick={prevStep}
            className="flex items-center"
          >
            <ChevronLeft className="mr-2 h-4 w-4" /> Précédent
          </Button>
        )}
        {step < totalSteps && (
          <Button
            onClick={nextStep}
            disabled={
              (step === 1 && !canProceedToStep2) ||
              (step === 2 && !canProceedToStep3) ||
              (step === 3 && !canProceedToStep4)
            }
            className="ml-auto flex items-center"
          >
            Suivant <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        )}
        {step === 4 && method === 'CARD' && (
          <Button
            disabled={!acceptTerms || isLoading}
            className="ml-auto"
            onClick={() =>
              initiateCardPayment(createPayload('EUR', session?.user.phone))
            }
          >
            {isLoading ? 'Traitement...' : 'Payer'}
          </Button>
        )}
      </div>
    </div>
  );
}

export default FormContent;
