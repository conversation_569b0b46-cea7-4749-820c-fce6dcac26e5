'use client';
import { Input } from '@/components/ui/input';
import { z } from 'zod';
import { SubmitHandler, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2 } from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import axios, { AxiosError } from 'axios';
import { AGGREGATORS, initGeneralPayment } from '@/lib/payment';
import { useToast } from '@/components/ui/use-toast';
import { useSession } from 'next-auth/react';
import { usePath } from '@/hooks/use-path';
import { CombinedType } from '@/types';
import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

const verfifCode = async (code: string) => {
  try {
    const { data } = await axios.post(
      `https://abjectof-conoda2.onrender.com/api/user/check-parrainCode`,
      {
        codeParrain: code,
      },
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      },
    );

    return { data, error: false };
  } catch (error) {
    return { data: null, error };
  }
};

interface PhoneFormProps {
  buttonText?: string;
  callbackUrl?: string;
  onSuccess?: () => void;
  payload: Omit<Partial<CombinedType>, 'phone'>;
  method: any;
  acceptPromo?: boolean;
}

export default function PhoneForm({
  buttonText,
  callbackUrl,
  payload,
  method,
  onSuccess,
  acceptPromo,
}: PhoneFormProps) {
  const { toast } = useToast();
  const { data: session } = useSession();
  const router = useRouter();
  const path = usePath();
  const phoneSchema = z.object({
    phone: z.string().startsWith('6').length(9),
    parrain: z.string().optional(),
    acceptTerms: z.coerce.boolean(),
  });
  type PhoneFormValues = z.infer<typeof phoneSchema>;
  const form = useForm<PhoneFormValues>({
    mode: 'onBlur',
    resolver: zodResolver(phoneSchema),
    defaultValues: {
      phone: '',
      parrain: 'objectif',
    },
  });
  const { setError } = form;
  const getDial = (method: any): string => {
    let dial = '';
    switch (method) {
      case 'MTN':
        dial = '*126#';
        break;
      case 'ORANGE':
        dial = '#150*50#';
        break;
      default:
        break;
    }
    return dial;
  };
  const checkParrainCode = async (code: string) => {
    try {
      const res = await verfifCode(code);
      if (res.error) {
        setError(
          'parrain',
          { message: 'Impossible de verifier ce code promo' },
          { shouldFocus: true },
        );
        return { error: true };
      }
      if (!res.data.isValid) {
        setError(
          'parrain',
          { message: "Ce code promo n'existe pas ou a été modifié" },
          { shouldFocus: true },
        );
        return { error: true };
      }
      return { error: false };
    } catch (err) {
      setError(
        'parrain',
        { message: err instanceof Error ? err.message : 'Erreur inconnue.' },
        { shouldFocus: true },
      );
      return { error: true };
    }
  };
  const { mutate: initPayment, isLoading } = useMutation(
    async ({ phone, parrain }: PhoneFormValues) => {
      localStorage.setItem('USER_INFO', JSON.stringify(session));
      const PAYLOAD: any = {
        ...payload,
        phone: Number(phone),
        parrain,
        amount: payload.amount,
      };
      return await initGeneralPayment('mobile', PAYLOAD, AGGREGATORS, path);
    },
    {
      onError(error) {
        console.log(error);
        if (error instanceof AxiosError) {
          if (error.code === 'ERR_NETWORK') {
            toast({
              title: "Impossible d'effectuer votre achat ",
              description: 'Vérifiez votre connexion internet',
              variant: 'destructive',
            });
            return;
          }
        }
        return toast({
          title: `Impossible d'effectuer votre achat`,
          description: 'Reessayer plus tard',
          variant: 'destructive',
        });
      },
      async onSuccess(data) {
        toast({
          title: 'Votre achat a été initié',
          description: `Composez ${getDial(
            method,
          )} et validez avec votre code secret.`,
        });
        if (onSuccess) {
          onSuccess();
        } else {
          router.push(
            `/verify-checkout?callbackUrl=${
              callbackUrl ?? '/'
            }&ref=${data.data.result.transaction_ref}`,
          );
        }
      },
    },
  );
  const onSubmit: SubmitHandler<PhoneFormValues> = async (values, e) => {
    if (values.parrain && values.parrain !== 'objectif') {
      const res = await checkParrainCode(values.parrain);
      if (res.error) {
        return;
      }
    }
    if (!values.acceptTerms) {
      setError(
        'acceptTerms',
        { message: 'Il faut accepter cette condition' },
        { shouldFocus: true },
      );
      return;
    }
    e?.preventDefault();
    initPayment(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="grid space-y-6">
        {acceptPromo ? (
          <FormField
            control={form.control}
            name="parrain"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="parrain">Code promo </FormLabel>
                <FormControl>
                  <Input
                    id="parrain"
                    placeholder={`Entrez votre code promo`}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        ) : null}
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="phone">
                Numéro de téléphone{' '}
                <sup className="text-lg font-bold text-red-500">*</sup>
              </FormLabel>
              <FormControl>
                <Input
                  id="phone"
                  type="number"
                  placeholder={` Numéro de téléphone ${method.toLowerCase()}`}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="items-top my-2 flex space-x-2">
          <div className="flex">
            <FormField
              control={form.control}
              name="acceptTerms"
              render={({ field }) => (
                <FormItem className="flex w-full gap-2">
                  <FormControl>
                    <Checkbox
                      id="terms1"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-1.5 text-left leading-none">
            <label
              htmlFor="terms1"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {"J'accepte les conditions."}
              <sup className="text-lg font-bold text-red-500">*</sup>
            </label>
            <p className="text-sm text-muted-foreground">
              En cochant cette case vous acceptez nos{' '}
              <Link
                href={'/condition-remboursement'}
                className="underline underline-offset-4 hover:text-primary"
              >
                conditions de remboursement.
              </Link>{' '}
            </p>
          </div>
        </div>
        <Button
          disabled={isLoading || !form.formState.isValid}
          type="submit"
          className="relative mt-3 w-full overflow-hidden"
        >
          <div
            className={`absolute inset-0 bg-primary ${
              isLoading ? 'flex items-center justify-center' : 'hidden'
            }`}
          >
            <Loader2 className={`h-4 w-4 animate-spin`} />
          </div>
          {buttonText || 'Continuer'}
        </Button>
      </form>
    </Form>
  );
}
